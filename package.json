{"name": "mcp-client-typescript", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"build": "tsc && chmod 755 build/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@anthropic-ai/sdk": "^0.52.0", "@modelcontextprotocol/sdk": "^1.12.0", "dotenv": "^16.5.0", "openai": "^4.103.0"}, "devDependencies": {"@types/node": "^22.15.21", "typescript": "^5.8.3"}}